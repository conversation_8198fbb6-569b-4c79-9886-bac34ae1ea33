"use client";
import React from "react";
import SettingsLabel from "../../components/settings-label";
import BillingHeader from "./components/billing-header";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import BillingPlanCard from "./components/billing-plan-card";
import FeatureList from "./components/billing-feature-list";
import BilllingAICreditBalance from "./components/billing-ai-credit-balance";
import BillingTopUpCredits from "./components/billing-top-up-credit";
import AICreditsUsage from "./components/billing-ai-credit-usage-table";

const page = () => {
  return (
    <div>
      <SettingsLabel />
      <div className="p-4">
        <div className="mb-6">
          <h1 className="text-base font-semibold">
            Your Organisation Billing Information
          </h1>
          <p className="text-sm text-[#344054]">
            Securely manage your organization’s billing details, payment
            history, and subscriptions.
          </p>
        </div>

        <BillingHeader
          title="Telex Free"
          description="You are enjoying the full Telex experience with ability to add as many users to your organisation."
          onComparePlan={() => console.log("View Starter")}
        />

        <Tabs defaultValue="plan-details" className="w-full mt-6">
          <div className="border-b border-gray-200">
            <TabsList className="flex w-fit bg-white rounded-none h-auto p-0 space-x-10">
              <TabsTrigger
                value="plan-details"
                className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors"
              >
                Plan Details
              </TabsTrigger>
              <TabsTrigger
                value="ai-credits"
                className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors"
              >
                AI Credits
              </TabsTrigger>
              <TabsTrigger
                value="payment-history"
                className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors"
              >
                Payment History
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="plan-details" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              <div className="col-span-2 h-full">
                <BillingPlanCard
                  planName="Telex Free"
                  description="For freelancers, or small early-stage teams who want to explore automation without platform fees."
                  price={0}
                  currency="$"
                  period="per month"
                  usersAdded={1}
                  storageUsed={0.89}
                  totalStorage={10}
                  storageUnit="GB"
                />
              </div>
              <div className="col-span-3 h-full">
                <FeatureList
                  features={[
                    "No user limit",
                    "No monthly platform charge",
                    "Access to all free agents",
                    "Pay as you go fee per paid agent usage",
                    "10GB all-time data storage",
                  ]}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="ai-credits" className="mt-6 ">
            <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
              <div className="col-span-2 h-full">
                <BilllingAICreditBalance
                  totalCredit={4000}
                  creditUsed={1550}
                  creditUnit="Credits"
                />
              </div>
              <div className="col-span-4 h-full">
                <BillingTopUpCredits creditPrice={1} creditsPerDollar={10} />
              </div>
            </div>
            <AICreditsUsage />
          </TabsContent>

          <TabsContent value="payment-history" className="mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Payment History</h3>
              <div className="bg-white border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">
                          Date
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">
                          Description
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">
                          Amount
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="py-3 px-4 text-gray-900">
                          June 15, 2025
                        </td>
                        <td className="py-3 px-4 text-gray-900">
                          Pro Plan - Monthly
                        </td>
                        <td className="py-3 px-4 text-gray-900">$29.99</td>
                        <td className="py-3 px-4">
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                            Paid
                          </span>
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4 text-gray-900">
                          May 15, 2025
                        </td>
                        <td className="py-3 px-4 text-gray-900">
                          Pro Plan - Monthly
                        </td>
                        <td className="py-3 px-4 text-gray-900">$29.99</td>
                        <td className="py-3 px-4">
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                            Paid
                          </span>
                        </td>
                      </tr>
                      <tr>
                        <td className="py-3 px-4 text-gray-900">
                          April 15, 2025
                        </td>
                        <td className="py-3 px-4 text-gray-900">
                          Pro Plan - Monthly
                        </td>
                        <td className="py-3 px-4 text-gray-900">$29.99</td>
                        <td className="py-3 px-4">
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                            Paid
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default page;
