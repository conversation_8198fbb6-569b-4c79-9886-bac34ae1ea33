import React from "react";
import { HeaderProps } from "../type";

const BillingHeader: React.FC<HeaderProps> = ({
  title,
  nextBillingDate,
  onComparePlan,
  description,
}) => {
  return (
    <div className="flex flex-col justify-between items-center p-3 bg-[#F1F1FE] border rounded-xl space-y-3">
      <div className="w-full flex items-center justify-between">
        <h1 className="text-xl font-bold text-gray-900">{title}</h1>
        {nextBillingDate && (
          <p className="text-sm text-gray-500">
            Next Billing Date: {nextBillingDate}
          </p>
        )}
      </div>
      <div className="w-full flex items-center justify-between bg-white  rounded-xl ps-3 pe-1 py-1">
        <div className=" ">
          <p className="text-gray-600 text-sm ">{description}</p>
        </div>
        <button
          onClick={onComparePlan}
          className="px-4 py-2 border border-[#8860F8] text-[#7141F8] rounded-md hover:bg-purple-50 transition-colors text-sm"
        >
          Compare Plans
        </button>
      </div>
    </div>
  );
};

export default BillingHeader;
