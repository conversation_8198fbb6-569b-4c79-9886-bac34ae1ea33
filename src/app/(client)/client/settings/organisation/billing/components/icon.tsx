import React, { JSX } from "react";

const FeatureBullets = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgprops}
    >
      <path
        d="M13.4291 11.2575C13.3953 11.314 13.3506 11.3632 13.2978 11.4024C13.2449 11.4416 13.1848 11.47 13.1209 11.4859C13.057 11.5018 12.9906 11.505 12.9255 11.4952C12.8604 11.4853 12.7979 11.4628 12.7416 11.4288L8.50032 8.88313V13.5C8.50032 13.6326 8.44764 13.7598 8.35388 13.8536C8.26011 13.9473 8.13293 14 8.00032 14C7.86771 14 7.74054 13.9473 7.64677 13.8536C7.553 13.7598 7.50032 13.6326 7.50032 13.5V8.88313L3.25782 11.4288C3.20149 11.4634 3.13887 11.4864 3.07356 11.4967C3.00825 11.5069 2.94156 11.5041 2.87734 11.4884C2.81313 11.4727 2.75266 11.4444 2.69945 11.4052C2.64623 11.366 2.60133 11.3166 2.56732 11.2599C2.53331 11.2032 2.51089 11.1404 2.50134 11.075C2.49179 11.0095 2.49531 10.9429 2.51168 10.8788C2.52806 10.8148 2.55698 10.7546 2.59676 10.7018C2.63654 10.649 2.6864 10.6047 2.74345 10.5712L7.02845 8L2.74345 5.42875C2.6864 5.39535 2.63654 5.35096 2.59676 5.29817C2.55698 5.24537 2.52806 5.18521 2.51168 5.12116C2.49531 5.05712 2.49179 4.99046 2.50134 4.92505C2.51089 4.85963 2.53331 4.79676 2.56732 4.74007C2.60133 4.68339 2.64623 4.634 2.69945 4.59478C2.75266 4.55556 2.81313 4.52728 2.87734 4.51158C2.94156 4.49588 3.00825 4.49307 3.07356 4.50331C3.13887 4.51356 3.20149 4.53665 3.25782 4.57125L7.50032 7.11688V2.5C7.50032 2.36739 7.553 2.24021 7.64677 2.14645C7.74054 2.05268 7.86771 2 8.00032 2C8.13293 2 8.26011 2.05268 8.35388 2.14645C8.44764 2.24021 8.50032 2.36739 8.50032 2.5V7.11688L12.7428 4.57125C12.7991 4.53665 12.8618 4.51356 12.9271 4.50331C12.9924 4.49307 13.0591 4.49588 13.1233 4.51158C13.1875 4.52728 13.248 4.55556 13.3012 4.59478C13.3544 4.634 13.3993 4.68339 13.4333 4.74007C13.4673 4.79676 13.4898 4.85963 13.4993 4.92505C13.5089 4.99046 13.5053 5.05712 13.489 5.12116C13.4726 5.18521 13.4437 5.24537 13.4039 5.29817C13.3641 5.35096 13.3142 5.39535 13.2572 5.42875L8.9722 8L13.2572 10.5712C13.3136 10.605 13.3627 10.6495 13.4019 10.7023C13.441 10.7551 13.4694 10.815 13.4854 10.8788C13.5013 10.9425 13.5046 11.0087 13.4949 11.0737C13.4852 11.1387 13.4629 11.2012 13.4291 11.2575Z"
        fill="#00CC5F"
      />
    </svg>
  );
};

const Money = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgprops}
    >
      <path
        d="M8 5.5C7.50555 5.5 7.0222 5.64662 6.61107 5.92133C6.19995 6.19603 5.87952 6.58648 5.6903 7.04329C5.50108 7.50011 5.45157 8.00277 5.54804 8.48773C5.6445 8.97268 5.8826 9.41814 6.23223 9.76777C6.58186 10.1174 7.02732 10.3555 7.51227 10.452C7.99723 10.5484 8.49989 10.4989 8.95671 10.3097C9.41352 10.1205 9.80397 9.80005 10.0787 9.38893C10.3534 8.9778 10.5 8.49445 10.5 8C10.5 7.33696 10.2366 6.70107 9.76777 6.23223C9.29893 5.76339 8.66304 5.5 8 5.5ZM8 9.5C7.70333 9.5 7.41332 9.41203 7.16664 9.2472C6.91997 9.08238 6.72771 8.84811 6.61418 8.57403C6.50065 8.29994 6.47094 7.99834 6.52882 7.70736C6.5867 7.41639 6.72956 7.14912 6.93934 6.93934C7.14912 6.72956 7.41639 6.5867 7.70736 6.52882C7.99834 6.47094 8.29994 6.50065 8.57403 6.61418C8.84811 6.72771 9.08238 6.91997 9.2472 7.16664C9.41203 7.41332 9.5 7.70333 9.5 8C9.5 8.39782 9.34196 8.77936 9.06066 9.06066C8.77936 9.34196 8.39782 9.5 8 9.5ZM15 3.5H1C0.867392 3.5 0.740215 3.55268 0.646447 3.64645C0.552678 3.74021 0.5 3.86739 0.5 4V12C0.5 12.1326 0.552678 12.2598 0.646447 12.3536C0.740215 12.4473 0.867392 12.5 1 12.5H15C15.1326 12.5 15.2598 12.4473 15.3536 12.3536C15.4473 12.2598 15.5 12.1326 15.5 12V4C15.5 3.86739 15.4473 3.74021 15.3536 3.64645C15.2598 3.55268 15.1326 3.5 15 3.5ZM12.1031 11.5H3.89687C3.729 10.9323 3.42175 10.4155 3.00311 9.99689C2.58447 9.57825 2.06775 9.271 1.5 9.10312V6.89687C2.06775 6.729 2.58447 6.42175 3.00311 6.00311C3.42175 5.58447 3.729 5.06775 3.89687 4.5H12.1031C12.271 5.06775 12.5782 5.58447 12.9969 6.00311C13.4155 6.42175 13.9323 6.729 14.5 6.89687V9.10312C13.9323 9.271 13.4155 9.57825 12.9969 9.99689C12.5782 10.4155 12.271 10.9323 12.1031 11.5ZM14.5 5.83563C13.9003 5.57775 13.4223 5.09973 13.1644 4.5H14.5V5.83563ZM2.83562 4.5C2.57774 5.09973 2.09973 5.57775 1.5 5.83563V4.5H2.83562ZM1.5 10.1644C2.09973 10.4223 2.57774 10.9003 2.83562 11.5H1.5V10.1644ZM13.1644 11.5C13.4223 10.9003 13.9003 10.4223 14.5 10.1644V11.5H13.1644Z"
        fill="#667085"
      />
    </svg>
  );
};

type IconType = "feature-bullets" | "money";

type Icon = {
  name: IconType;
  svgProps: React.SVGProps<SVGSVGElement>;
};

const Icons: React.FC<Icon> = ({ name, svgProps }) => {
  const icons: Record<IconType, JSX.Element> = {
    "feature-bullets": <FeatureBullets {...svgProps} />,
    money: <Money {...svgProps} />,
  };

  return icons[name];
};

export default Icons;
