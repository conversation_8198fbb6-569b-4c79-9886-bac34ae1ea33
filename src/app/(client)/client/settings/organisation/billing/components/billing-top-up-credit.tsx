import React from "react";
import cogoToast from "cogo-toast";
import { ChevronDown, ChevronUp, Loader2 } from "lucide-react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { TopUpCreditsProps } from "../type";

const BillingTopUpCredits: React.FC<TopUpCreditsProps> = ({
  creditPrice,
  creditsPerDollar,
  // eslint-disable-next-line
  onPurchase,
}) => {
  const [creditAmount, setCreditAmount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [purchaseCount, setPurchaseCount] = useState(0);

  const totalCost = creditAmount * creditPrice;

  const incrementCredits = () => setCreditAmount((prev) => prev + 10);
  const decrementCredits = () =>
    setCreditAmount((prev) => Math.max(0, prev - 10));

  const handlePurchase = async () => {
    if (creditAmount === 0) return;

    setIsLoading(true);

    await new Promise((resolve) => setTimeout(resolve, 2000));

    const isSuccess = purchaseCount % 2 === 0;
    setPurchaseCount((prev) => prev + 1);

    if (isSuccess) {
      cogoToast.success("Your organisation has received 100 credits for $10");
      setCreditAmount(0);
    } else {
      cogoToast.error(
        "There was an error processing your request for 1,000 credits. Please try again later."
      );
    }

    setIsLoading(false);
  };

  return (
    <div className="bg-white rounded-xl border border-gray-200 py-6 shadow-sm">
      <div className="border-b px-6">
        <div className="flex justify-between items-center mb-0 ">
          <h3 className="text-xl font-bold text-gray-900">Top-Up AI Credits</h3>
          <span className="text-sm text-black bg-[#F2F4F7] font-bold px-4 py-2 rounded-lg">
            ${creditPrice} • {creditsPerDollar}{" "}
            <span className="text-gray-500 font-[300]">AI credits</span>
          </span>
        </div>
        <p className="text-gray-600 text-sm mb-3">
          Stay connected when you have enough to go round!
        </p>
      </div>

      <div className="px-6 pt-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          AI Credits
        </label>
        <div className="grid grid-cols-6 gap-4">
          <div className="col-span-4 h-full">
            <div className="flex items-center space-x-1">
              <Input
                type="number"
                value={creditAmount}
                onChange={(e) =>
                  setCreditAmount(Math.max(0, parseInt(e.target.value) || 0))
                }
                className="flex-1 px-3 py-2 h-[48px] border border-[#E6EAEF] rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="0"
              />
              <div className="flex flex-col space-y-1">
                <Button
                  onClick={incrementCredits}
                  className="px-3 !py-[2px] h-[21px] border border-[#E6EAEF] rounded-sm hover:bg-gray-50 text-gray-600"
                >
                  <ChevronUp size={16} />
                </Button>
                <Button
                  onClick={decrementCredits}
                  disabled={creditAmount === 0}
                  className="px-3 !py-[2px] h-[21px] border border-[#E6EAEF] rounded-sm hover:bg-gray-50 text-gray-600"
                >
                  <ChevronDown size={16} />
                </Button>
              </div>
            </div>
          </div>
          <div className="col-span-2 h-full flex items-end">
            <Button
              // onClick={() => onPurchase?.(creditAmount)}
              onClick={handlePurchase}
              disabled={creditAmount === 0 || isLoading}
              className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                creditAmount === 0
                  ? "bg-[#7141F8] text-white cursor-not-allowed"
                  : "bg-[#7141F8] text-white"
              }`}
            >
              {isLoading ? (
                <div className="flex gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Processing...</span>
                </div>
              ) : (
                <span>
                  Pay ${totalCost.toFixed(0)} •{" "}
                  {new Intl.NumberFormat("en-US").format(
                    creditAmount * creditsPerDollar
                  )}{" "}
                  AI Credits
                </span>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingTopUpCredits;
