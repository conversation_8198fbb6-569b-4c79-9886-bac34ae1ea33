import { AIUsageRecord } from "../type";

export const mockData: AIUsageRecord[] = [
  {
    id: "1",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "#content-ideas",
    type: "Channel",
    aiCredits: 5,
    dateTime: "29/04/2025 at 16:20",
    status: "online",
  },
  {
    id: "2",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "@<PERSON> Garner",
    type: "Private Chat",
    aiCredits: 4,
    dateTime: "29/04/2025 at 15:05",
    status: "online",
  },
  {
    id: "3",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@<PERSON> Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "29/04/2025 at 14:47",
    status: "online",
  },
  {
    id: "4",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "#brand-marketing",
    type: "Channel",
    aiCredits: 5,
    dateTime: "28/04/2025 at 16:20",
    status: "online",
  },
  {
    id: "5",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "#july-campaign",
    type: "Channel",
    aiCredits: 5,
    dateTime: "28/04/2025 at 14:13",
    status: "online",
  },
  {
    id: "6",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "@Jennifer Garner",
    type: "Private Chat",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:12",
    status: "online",
  },
  {
    id: "7",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Jennifer Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:01",
    status: "online",
  },
  {
    id: "8",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Jennifer Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:01",
    status: "online",
  },
  {
    id: "9",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Jennifer Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:01",
    status: "online",
  },
  {
    id: "10",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Jennifer Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:01",
    status: "online",
  },
  {
    id: "11",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "#general",
    type: "Channel",
    aiCredits: 3,
    dateTime: "27/04/2025 at 18:45",
    status: "online",
  },
  {
    id: "12",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "@Michael Chen",
    type: "Private Chat",
    aiCredits: 7,
    dateTime: "27/04/2025 at 17:30",
    status: "online",
  },
  {
    id: "13",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "#tech-support",
    type: "Channel",
    aiCredits: 6,
    dateTime: "27/04/2025 at 16:15",
    status: "online",
  },
  {
    id: "14",
    agentName: "Pulse - Analytics Bot",
    agentType: "analytics",
    nameOwner: "@Sarah Wilson",
    type: "Private Group",
    aiCredits: 8,
    dateTime: "27/04/2025 at 15:22",
    status: "online",
  },
  {
    id: "15",
    agentName: "Zephyr - Scheduler",
    agentType: "scheduler",
    nameOwner: "#project-alpha",
    type: "Channel",
    aiCredits: 4,
    dateTime: "27/04/2025 at 14:10",
    status: "offline",
  },
  {
    id: "16",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "@David Rodriguez",
    type: "Private Chat",
    aiCredits: 2,
    dateTime: "27/04/2025 at 13:55",
    status: "online",
  },
  {
    id: "17",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "#international-team",
    type: "Channel",
    aiCredits: 9,
    dateTime: "27/04/2025 at 12:40",
    status: "online",
  },
  {
    id: "18",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Emma Thompson",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "27/04/2025 at 11:25",
    status: "online",
  },
  {
    id: "19",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "@Alex Johnson",
    type: "Private Chat",
    aiCredits: 6,
    dateTime: "26/04/2025 at 19:30",
    status: "online",
  },
  {
    id: "20",
    agentName: "Pulse - Analytics Bot",
    agentType: "analytics",
    nameOwner: "#data-science",
    type: "Channel",
    aiCredits: 10,
    dateTime: "26/04/2025 at 18:15",
    status: "online",
  },
  {
    id: "21",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "#community",
    type: "Channel",
    aiCredits: 3,
    dateTime: "26/04/2025 at 17:45",
    status: "online",
  },
  {
    id: "22",
    agentName: "Zephyr - Scheduler",
    agentType: "scheduler",
    nameOwner: "@Lisa Park",
    type: "Private Group",
    aiCredits: 4,
    dateTime: "26/04/2025 at 16:20",
    status: "offline",
  },
  {
    id: "23",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "@Robert Kim",
    type: "Private Chat",
    aiCredits: 2,
    dateTime: "26/04/2025 at 15:10",
    status: "online",
  },
  {
    id: "24",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "#global-marketing",
    type: "Channel",
    aiCredits: 8,
    dateTime: "26/04/2025 at 14:35",
    status: "online",
  },
  {
    id: "25",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "@Maria Garcia",
    type: "Private Group",
    aiCredits: 7,
    dateTime: "26/04/2025 at 13:50",
    status: "online",
  },
  {
    id: "26",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "#development",
    type: "Channel",
    aiCredits: 5,
    dateTime: "26/04/2025 at 12:25",
    status: "online",
  },
  {
    id: "27",
    agentName: "Pulse - Analytics Bot",
    agentType: "analytics",
    nameOwner: "@James Wilson",
    type: "Private Chat",
    aiCredits: 9,
    dateTime: "26/04/2025 at 11:40",
    status: "online",
  },
  {
    id: "28",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "@Nina Patel",
    type: "Private Group",
    aiCredits: 3,
    dateTime: "25/04/2025 at 20:15",
    status: "online",
  },
  {
    id: "29",
    agentName: "Zephyr - Scheduler",
    agentType: "scheduler",
    nameOwner: "#product-team",
    type: "Channel",
    aiCredits: 4,
    dateTime: "25/04/2025 at 19:30",
    status: "offline",
  },
  {
    id: "30",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "@Tom Anderson",
    type: "Private Chat",
    aiCredits: 2,
    dateTime: "25/04/2025 at 18:45",
    status: "online",
  },
  {
    id: "31",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "#customer-support",
    type: "Channel",
    aiCredits: 6,
    dateTime: "25/04/2025 at 17:20",
    status: "online",
  },
  {
    id: "32",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "@Rachel Green",
    type: "Private Group",
    aiCredits: 8,
    dateTime: "25/04/2025 at 16:10",
    status: "online",
  },
  {
    id: "33",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Kevin Lee",
    type: "Private Chat",
    aiCredits: 5,
    dateTime: "25/04/2025 at 15:35",
    status: "online",
  },
  {
    id: "34",
    agentName: "Pulse - Analytics Bot",
    agentType: "analytics",
    nameOwner: "#finance",
    type: "Channel",
    aiCredits: 7,
    dateTime: "25/04/2025 at 14:50",
    status: "online",
  },
  {
    id: "35",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "@Sophie Turner",
    type: "Private Group",
    aiCredits: 3,
    dateTime: "25/04/2025 at 13:25",
    status: "online",
  },
  {
    id: "36",
    agentName: "Zephyr - Scheduler",
    agentType: "scheduler",
    nameOwner: "@Daniel Brown",
    type: "Private Chat",
    aiCredits: 4,
    dateTime: "25/04/2025 at 12:40",
    status: "offline",
  },
  {
    id: "37",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "#announcements",
    type: "Channel",
    aiCredits: 2,
    dateTime: "25/04/2025 at 11:15",
    status: "online",
  },
  {
    id: "38",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "@Isabella Martinez",
    type: "Private Group",
    aiCredits: 9,
    dateTime: "24/04/2025 at 22:30",
    status: "online",
  },
  {
    id: "39",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "#research",
    type: "Channel",
    aiCredits: 6,
    dateTime: "24/04/2025 at 21:45",
    status: "online",
  },
  {
    id: "40",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Christopher Davis",
    type: "Private Chat",
    aiCredits: 5,
    dateTime: "24/04/2025 at 20:20",
    status: "online",
  },
];
